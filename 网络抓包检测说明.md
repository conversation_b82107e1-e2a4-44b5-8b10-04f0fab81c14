# 网络抓包检测功能

## 功能说明

在脚本初始化时自动检测手机是否开启了网络代理抓包功能，如果检测到抓包活动，会提示用户并自动退出程序。

## 检测内容

### 网络代理检测
- **HTTP代理设置**：`settings get global http_proxy`
- **HTTPS代理设置**：`settings get global https_proxy`  
- **系统代理属性**：`getprop net.http_proxy`
- **全局代理配置**：`settings get global global_http_proxy_host`

## 工作流程

1. 脚本启动后，首先初始化日志模块
2. 立即执行网络抓包检测
3. 如果检测到代理设置：
   - 显示安全警告对话框
   - 提示用户关闭网络代理
   - 5秒后自动退出程序
4. 如果未检测到代理，继续正常初始化

## 文件说明

- **`main.js`**：主脚本，已集成网络抓包检测功能
- **`packet_capture_test.js`**：独立测试脚本，用于验证检测功能

## 使用方法

### 自动检测
运行主脚本时会自动进行网络抓包检测，无需额外操作。

### 独立测试  
运行 `packet_capture_test.js` 可以单独测试检测功能：
1. 运行测试脚本
2. 点击"开始检测"按钮
3. 查看检测结果

## 触发条件

以下情况会触发网络抓包警告：
- 系统配置了HTTP代理
- 系统配置了HTTPS代理  
- 检测到系统代理属性
- 检测到全局代理配置

## 安全机制

- **保守策略**：检测失败时默认假设存在风险并退出
- **用户提示**：显示详细警告信息和解决方法
- **自动退出**：防止用户忽略安全警告

## 注意事项

- 需要相应的系统权限执行检测命令
- 某些Android版本的命令可能有差异
- 检测过程会略微增加启动时间